package fr.enedis.i2r.system.watchdog;

import fr.enedis.i2r.system.SystemConfiguration;
import fr.enedis.i2r.system.ports.WatchdogSocketPort;
import fr.enedis.i2r.system.systemd.WatchdogService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

import static org.junit.jupiter.api.Assertions.*;

class WatchdogIntegrationTest {

    private ThreadHeartbeatRegistry registry;
    private MockWatchdogSocketPort mockSocket;
    private SystemConfiguration systemConfiguration;

    @BeforeEach
    void setUp() {
        registry = new ThreadHeartbeatRegistry(Duration.ofSeconds(2));
        mockSocket = new MockWatchdogSocketPort();
        systemConfiguration = new SystemConfiguration(
            null, null, Duration.ofSeconds(1), Duration.ofSeconds(2), Duration.ofSeconds(1)
        );
    }

    @Test
    void testWatchdogWithHealthyThreads() throws InterruptedException {
        // Create and start some monitored threads
        TestThread thread1 = new TestThread("thread1", Duration.ofSeconds(3));
        TestThread thread2 = new TestThread("thread2", Duration.ofSeconds(3));

        thread1.setHeartbeatRegistry(registry);
        thread2.setHeartbeatRegistry(registry);

        Thread executor1 = new Thread(thread1);
        Thread executor2 = new Thread(thread2);

        executor1.start();
        executor2.start();

        // Wait for threads to register
        Thread.sleep(100);

        // Start watchdog service
        WatchdogService watchdog = new WatchdogService(mockSocket, systemConfiguration, registry);
        Thread watchdogThread = new Thread(watchdog);
        watchdogThread.start();

        // Let it run for a few cycles
        Thread.sleep(3000);

        // Stop everything
        thread1.stop();
        thread2.stop();
        watchdog.stop();

        executor1.join(1000);
        executor2.join(1000);
        watchdogThread.join(1000);

        // Verify healthy status messages were sent
        assertTrue(mockSocket.getHeartbeatCount() > 0);
        assertTrue(mockSocket.getLastMessage().contains("threads healthy"));
    }

    @Test
    void testWatchdogWithUnhealthyThread() throws InterruptedException {
        // Create threads with different timeouts
        TestThread healthyThread = new TestThread("healthy", Duration.ofSeconds(5));
        TestThread unhealthyThread = new TestThread("unhealthy", Duration.ofMillis(500));

        healthyThread.setHeartbeatRegistry(registry);
        unhealthyThread.setHeartbeatRegistry(registry);

        Thread executor1 = new Thread(healthyThread);
        Thread executor2 = new Thread(unhealthyThread);

        executor1.start();
        executor2.start();

        // Wait for threads to register
        Thread.sleep(100);

        // Stop the unhealthy thread's heartbeats but keep it running
        unhealthyThread.stopHeartbeats();

        // Start watchdog service
        WatchdogService watchdog = new WatchdogService(mockSocket, systemConfiguration, registry);
        Thread watchdogThread = new Thread(watchdog);
        watchdogThread.start();

        // Wait for unhealthy detection
        Thread.sleep(2000);

        // Stop everything
        healthyThread.stop();
        unhealthyThread.stop();
        watchdog.stop();

        executor1.join(1000);
        executor2.join(1000);
        watchdogThread.join(1000);

        // Verify unhealthy status was detected
        assertTrue(mockSocket.getLastMessage().contains("unhealthy"));
    }

    @Test
    void testWatchdogServiceLifecycle() throws InterruptedException {
        WatchdogService watchdog = new WatchdogService(mockSocket, systemConfiguration, registry);
        Thread watchdogThread = new Thread(watchdog);

        watchdogThread.start();
        Thread.sleep(500);

        assertTrue(watchdogThread.isAlive());
        assertEquals(1, mockSocket.getInitCount());

        watchdog.stop();
        watchdogThread.join(1000);

        assertFalse(watchdogThread.isAlive());
    }

    // Mock implementation for testing
    private static class MockWatchdogSocketPort implements WatchdogSocketPort {
        private final AtomicInteger initCount = new AtomicInteger(0);
        private final AtomicInteger heartbeatCount = new AtomicInteger(0);
        private final AtomicReference<String> lastMessage = new AtomicReference<>("");

        @Override
        public void init() {
            initCount.incrementAndGet();
        }

        @Override
        public void heartbeat() {
            heartbeatCount.incrementAndGet();
        }

        @Override
        public void heartbeatWithMessage(String message) {
            heartbeatCount.incrementAndGet();
            lastMessage.set(message);
        }

        public int getInitCount() {
            return initCount.get();
        }

        public int getHeartbeatCount() {
            return heartbeatCount.get();
        }

        public String getLastMessage() {
            return lastMessage.get();
        }
    }

    // Test thread implementation
    private static class TestThread extends AbstractMonitoredThread {
        private volatile boolean sendHeartbeats = true;

        public TestThread(String threadName, Duration heartbeatTimeout) {
            super(threadName, heartbeatTimeout);
        }

        @Override
        protected void doRun() throws InterruptedException {
            while (isRunning()) {
                if (sendHeartbeats) {
                    sendHeartbeat("Running normally");
                }
                Thread.sleep(100);
            }
        }

        public void stopHeartbeats() {
            sendHeartbeats = false;
        }
    }
}
