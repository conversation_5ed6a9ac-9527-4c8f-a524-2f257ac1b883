package fr.enedis.i2r.system.watchdog;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class ThreadHeartbeatRegistryTest {

    private ThreadHeartbeatRegistry registry;
    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(5);

    @BeforeEach
    void setUp() {
        registry = new ThreadHeartbeatRegistry(DEFAULT_TIMEOUT);
    }

    @Test
    void testRegisterThread() {
        String threadId = "test-thread-1";
        String threadName = "Test Thread";

        registry.registerThread(threadId, threadName);

        assertTrue(registry.isThreadRegistered(threadId));
        assertEquals(threadName, registry.getThreadName(threadId));
        assertEquals(1, registry.getRegisteredThreadCount());
    }

    @Test
    void testRegisterThreadWithCustomTimeout() {
        String threadId = "test-thread-2";
        String threadName = "Test Thread 2";
        Duration customTimeout = Duration.ofSeconds(10);

        registry.registerThread(threadId, threadName, customTimeout);

        assertTrue(registry.isThreadRegistered(threadId));
        Map<String, ThreadHealthStatus> healthStatus = registry.getThreadHealthStatus();
        assertEquals(customTimeout, healthStatus.get(threadId).heartbeatTimeout());
    }

    @Test
    void testRecordHeartbeat() {
        String threadId = "test-thread-3";
        String threadName = "Test Thread 3";

        registry.registerThread(threadId, threadName);
        registry.recordHeartbeat(threadId);

        Map<String, ThreadHealthStatus> healthStatus = registry.getThreadHealthStatus();
        ThreadHealthStatus status = healthStatus.get(threadId);
        assertNotNull(status);
        assertTrue(status.isHealthy());
    }

    @Test
    void testRecordHeartbeatWithStatus() {
        String threadId = "test-thread-4";
        String threadName = "Test Thread 4";
        String statusMessage = "Processing data";

        registry.registerThread(threadId, threadName);
        registry.recordHeartbeat(threadId, statusMessage);

        Map<String, ThreadHealthStatus> healthStatus = registry.getThreadHealthStatus();
        ThreadHealthStatus status = healthStatus.get(threadId);
        assertNotNull(status);
        assertEquals(statusMessage, status.lastStatus());
    }

    @Test
    void testUnregisterThread() {
        String threadId = "test-thread-5";
        String threadName = "Test Thread 5";

        registry.registerThread(threadId, threadName);
        assertTrue(registry.isThreadRegistered(threadId));

        registry.unregisterThread(threadId);
        assertFalse(registry.isThreadRegistered(threadId));
        assertEquals(0, registry.getRegisteredThreadCount());
    }

    @Test
    void testGetUnhealthyThreads() throws InterruptedException {
        String healthyThreadId = "healthy-thread";
        String unhealthyThreadId = "unhealthy-thread";
        Duration shortTimeout = Duration.ofMillis(100);

        registry.registerThread(healthyThreadId, "Healthy Thread");
        registry.registerThread(unhealthyThreadId, "Unhealthy Thread", shortTimeout);

        // Record heartbeat for healthy thread
        registry.recordHeartbeat(healthyThreadId);

        // Wait for unhealthy thread to timeout
        Thread.sleep(150);

        Set<String> unhealthyThreads = registry.getUnhealthyThreads();
        assertFalse(unhealthyThreads.contains(healthyThreadId));
        assertTrue(unhealthyThreads.contains(unhealthyThreadId));
    }

    @Test
    void testGetThreadHealthStatus() {
        String threadId = "status-test-thread";
        String threadName = "Status Test Thread";

        registry.registerThread(threadId, threadName);
        registry.recordHeartbeat(threadId, "Running normally");

        Map<String, ThreadHealthStatus> healthStatus = registry.getThreadHealthStatus();
        ThreadHealthStatus status = healthStatus.get(threadId);

        assertNotNull(status);
        assertEquals(threadName, status.threadName());
        assertEquals("Running normally", status.lastStatus());
        assertTrue(status.isHealthy());
        assertNotNull(status.lastHeartbeat());
        assertNotNull(status.timeSinceLastHeartbeat());
        assertEquals(DEFAULT_TIMEOUT, status.heartbeatTimeout());
    }

    @Test
    void testRecordHeartbeatForUnregisteredThread() {
        String unregisteredThreadId = "unregistered-thread";

        // This should not throw an exception, just log a warning
        assertDoesNotThrow(() -> registry.recordHeartbeat(unregisteredThreadId));
    }

    @Test
    void testMultipleThreads() {
        String thread1Id = "thread-1";
        String thread2Id = "thread-2";
        String thread3Id = "thread-3";

        registry.registerThread(thread1Id, "Thread 1");
        registry.registerThread(thread2Id, "Thread 2");
        registry.registerThread(thread3Id, "Thread 3");

        assertEquals(3, registry.getRegisteredThreadCount());

        registry.recordHeartbeat(thread1Id, "Status 1");
        registry.recordHeartbeat(thread2Id, "Status 2");
        registry.recordHeartbeat(thread3Id, "Status 3");

        Map<String, ThreadHealthStatus> healthStatus = registry.getThreadHealthStatus();
        assertEquals(3, healthStatus.size());

        for (ThreadHealthStatus status : healthStatus.values()) {
            assertTrue(status.isHealthy());
        }
    }
}
