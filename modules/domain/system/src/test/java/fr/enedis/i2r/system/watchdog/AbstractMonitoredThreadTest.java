package fr.enedis.i2r.system.watchdog;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

class AbstractMonitoredThreadTest {

    private ThreadHeartbeatRegistry registry;
    private static final Duration TEST_TIMEOUT = Duration.ofSeconds(1);

    @BeforeEach
    void setUp() {
        registry = new ThreadHeartbeatRegistry(TEST_TIMEOUT);
    }

    @Test
    void testThreadRegistrationAndExecution() throws InterruptedException {
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch endLatch = new CountDownLatch(1);
        AtomicBoolean executed = new AtomicBoolean(false);

        TestMonitoredThread thread = new TestMonitoredThread("test-thread", TEST_TIMEOUT) {
            @Override
            protected void doRun() throws InterruptedException {
                startLatch.countDown();
                executed.set(true);
                sendHeartbeat("Test execution");
                endLatch.await(5, TimeUnit.SECONDS);
            }
        };

        thread.setHeartbeatRegistry(registry);
        
        Thread executor = new Thread(thread);
        executor.start();

        // Wait for thread to start
        assertTrue(startLatch.await(5, TimeUnit.SECONDS));
        assertTrue(executed.get());
        assertTrue(registry.isThreadRegistered(thread.getThreadId()));

        // Stop the thread
        thread.stop();
        endLatch.countDown();
        executor.join(5000);

        // Thread should be unregistered after stopping
        assertFalse(registry.isThreadRegistered(thread.getThreadId()));
    }

    @Test
    void testHeartbeatSending() throws InterruptedException {
        CountDownLatch heartbeatLatch = new CountDownLatch(2);
        
        TestMonitoredThread thread = new TestMonitoredThread("heartbeat-test", TEST_TIMEOUT) {
            @Override
            protected void doRun() throws InterruptedException {
                sendHeartbeat("First heartbeat");
                heartbeatLatch.countDown();
                Thread.sleep(100);
                sendHeartbeat("Second heartbeat");
                heartbeatLatch.countDown();
            }
        };

        thread.setHeartbeatRegistry(registry);
        
        Thread executor = new Thread(thread);
        executor.start();

        assertTrue(heartbeatLatch.await(5, TimeUnit.SECONDS));
        executor.join(5000);

        // Verify heartbeats were recorded
        assertFalse(registry.isThreadRegistered(thread.getThreadId())); // Should be unregistered after completion
    }

    @Test
    void testThreadInterruption() throws InterruptedException {
        CountDownLatch startLatch = new CountDownLatch(1);
        AtomicBoolean wasInterrupted = new AtomicBoolean(false);

        TestMonitoredThread thread = new TestMonitoredThread("interrupt-test", TEST_TIMEOUT) {
            @Override
            protected void doRun() throws InterruptedException {
                startLatch.countDown();
                try {
                    while (isRunning()) {
                        sendHeartbeat("Running");
                        Thread.sleep(100);
                    }
                } catch (InterruptedException e) {
                    wasInterrupted.set(true);
                    throw e;
                }
            }
        };

        thread.setHeartbeatRegistry(registry);
        
        Thread executor = new Thread(thread);
        executor.start();

        // Wait for thread to start
        assertTrue(startLatch.await(5, TimeUnit.SECONDS));

        // Interrupt the thread
        executor.interrupt();
        executor.join(5000);

        assertTrue(wasInterrupted.get());
        assertFalse(registry.isThreadRegistered(thread.getThreadId()));
    }

    @Test
    void testExceptionHandling() throws InterruptedException {
        CountDownLatch startLatch = new CountDownLatch(1);
        RuntimeException testException = new RuntimeException("Test exception");

        TestMonitoredThread thread = new TestMonitoredThread("exception-test", TEST_TIMEOUT) {
            @Override
            protected void doRun() throws InterruptedException {
                startLatch.countDown();
                sendHeartbeat("About to throw exception");
                throw testException;
            }
        };

        thread.setHeartbeatRegistry(registry);
        
        Thread executor = new Thread(thread);
        executor.start();

        assertTrue(startLatch.await(5, TimeUnit.SECONDS));
        executor.join(5000);

        // Thread should be unregistered even after exception
        assertFalse(registry.isThreadRegistered(thread.getThreadId()));
    }

    @Test
    void testThreadWithoutRegistry() throws InterruptedException {
        CountDownLatch executionLatch = new CountDownLatch(1);

        TestMonitoredThread thread = new TestMonitoredThread("no-registry-test", TEST_TIMEOUT) {
            @Override
            protected void doRun() throws InterruptedException {
                sendHeartbeat("This should not fail");
                executionLatch.countDown();
            }
        };

        // Don't set registry - should still work
        Thread executor = new Thread(thread);
        executor.start();

        assertTrue(executionLatch.await(5, TimeUnit.SECONDS));
        executor.join(5000);
    }

    @Test
    void testMultipleHeartbeatsWithStatus() throws InterruptedException {
        CountDownLatch completionLatch = new CountDownLatch(1);
        AtomicInteger heartbeatCount = new AtomicInteger(0);

        TestMonitoredThread thread = new TestMonitoredThread("status-test", TEST_TIMEOUT) {
            @Override
            protected void doRun() throws InterruptedException {
                for (int i = 0; i < 3; i++) {
                    sendHeartbeat("Status " + i);
                    heartbeatCount.incrementAndGet();
                    Thread.sleep(50);
                }
                completionLatch.countDown();
            }
        };

        thread.setHeartbeatRegistry(registry);
        
        Thread executor = new Thread(thread);
        executor.start();

        assertTrue(completionLatch.await(5, TimeUnit.SECONDS));
        executor.join(5000);

        assertEquals(3, heartbeatCount.get());
    }

    // Helper class for testing
    private static abstract class TestMonitoredThread extends AbstractMonitoredThread {
        public TestMonitoredThread(String threadName, Duration heartbeatTimeout) {
            super(threadName, heartbeatTimeout);
        }
    }
}
