package fr.enedis.i2r.system.watchdog;

import java.time.Duration;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Abstract base class for threads that need to be monitored by the watchdog system.
 * Provides default implementations for heartbeat management and thread identification.
 */
public abstract class AbstractMonitoredThread implements MonitoredThread {

    private static final Logger logger = LoggerFactory.getLogger(AbstractMonitoredThread.class);

    private final String threadId;
    private final String threadName;
    private final Duration heartbeatTimeout;
    private ThreadHeartbeatRegistry heartbeatRegistry;
    private volatile boolean running = true;

    /**
     * Creates a new monitored thread with a generated UUID as thread ID.
     *
     * @param threadName human-readable name for the thread
     * @param heartbeatTimeout maximum time allowed between heartbeats
     */
    protected AbstractMonitoredThread(String threadName, Duration heartbeatTimeout) {
        this(UUID.randomUUID().toString(), threadName, heartbeatTimeout);
    }

    /**
     * Creates a new monitored thread with a specific thread ID.
     *
     * @param threadId unique identifier for the thread
     * @param threadName human-readable name for the thread
     * @param heartbeatTimeout maximum time allowed between heartbeats
     */
    protected AbstractMonitoredThread(String threadId, String threadName, Duration heartbeatTimeout) {
        this.threadId = threadId;
        this.threadName = threadName;
        this.heartbeatTimeout = heartbeatTimeout;
    }

    @Override
    public final String getThreadId() {
        return threadId;
    }

    @Override
    public final String getThreadName() {
        return threadName;
    }

    @Override
    public final Duration getHeartbeatTimeout() {
        return heartbeatTimeout;
    }

    @Override
    public final void setHeartbeatRegistry(ThreadHeartbeatRegistry registry) {
        this.heartbeatRegistry = registry;
        if (registry != null) {
            registry.registerThread(threadId, threadName, heartbeatTimeout);
            logger.info("Thread {} registered with watchdog system", threadName);
        }
    }

    @Override
    public final void sendHeartbeat() {
        sendHeartbeat(null);
    }

    @Override
    public final void sendHeartbeat(String status) {
        if (heartbeatRegistry != null) {
            heartbeatRegistry.recordHeartbeat(threadId, status);
        }
    }

    @Override
    public final void run() {
        try {
            logger.info("Starting monitored thread: {}", threadName);
            sendHeartbeat("Starting");

            doRun();

        } catch (InterruptedException e) {
            logger.info("Thread {} was interrupted", threadName);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            logger.error("Error in monitored thread {}", threadName, e);
            sendHeartbeat("Error: " + e.getMessage());
        } finally {
            running = false;
            if (heartbeatRegistry != null) {
                heartbeatRegistry.unregisterThread(threadId);
            }
            logger.info("Monitored thread {} stopped", threadName);
        }
    }

    /**
     * The main execution logic for the thread.
     * Implementing classes should override this method instead of run().
     * This method should regularly call sendHeartbeat() to indicate the thread is alive.
     *
     * @throws InterruptedException if the thread is interrupted
     */
    protected abstract void doRun() throws InterruptedException;

    /**
     * Checks if the thread is still running.
     *
     * @return true if the thread is running
     */
    protected final boolean isRunning() {
        return running && !Thread.currentThread().isInterrupted();
    }

    /**
     * Requests the thread to stop gracefully.
     * The thread should check isRunning() regularly and exit when it returns false.
     */
    public final void stop() {
        logger.info("Stop requested for thread: {}", threadName);
        running = false;
    }

    /**
     * Gets the heartbeat registry associated with this thread.
     *
     * @return the heartbeat registry or null if not set
     */
    protected final ThreadHeartbeatRegistry getHeartbeatRegistry() {
        return heartbeatRegistry;
    }
}
