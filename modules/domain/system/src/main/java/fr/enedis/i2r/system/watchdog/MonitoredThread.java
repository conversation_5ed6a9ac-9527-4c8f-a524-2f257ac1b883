package fr.enedis.i2r.system.watchdog;

import java.time.Duration;

/**
 * Interface for threads that can be monitored by the watchdog system.
 * Implementing classes should regularly call sendHeartbeat() to indicate they are alive.
 */
public interface MonitoredThread extends Runnable {
    
    /**
     * Gets the unique identifier for this thread.
     * This should be consistent across the lifetime of the thread.
     * 
     * @return unique thread identifier
     */
    String getThreadId();
    
    /**
     * Gets the human-readable name for this thread.
     * 
     * @return thread name
     */
    String getThreadName();
    
    /**
     * Gets the maximum time allowed between heartbeats before the thread is considered unhealthy.
     * 
     * @return heartbeat timeout duration
     */
    Duration getHeartbeatTimeout();
    
    /**
     * Sends a heartbeat to indicate the thread is alive and functioning.
     * This method should be called regularly by the implementing thread.
     */
    void sendHeartbeat();
    
    /**
     * Sends a heartbeat with additional status information.
     * 
     * @param status current status or activity description
     */
    void sendHeartbeat(String status);
    
    /**
     * Sets the heartbeat registry that this thread should report to.
     * This is typically called during initialization.
     * 
     * @param registry the heartbeat registry
     */
    void setHeartbeatRegistry(ThreadHeartbeatRegistry registry);
}
