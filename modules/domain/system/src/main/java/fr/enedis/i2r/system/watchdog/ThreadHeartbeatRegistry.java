package fr.enedis.i2r.system.watchdog;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Thread-safe registry for managing heartbeats from monitored threads.
 * This class tracks the last heartbeat timestamp for each registered thread
 * and provides methods to check thread health based on configurable timeouts.
 */
public class ThreadHeartbeatRegistry {
    
    private static final Logger logger = LoggerFactory.getLogger(ThreadHeartbeatRegistry.class);
    
    private final ConcurrentMap<String, ThreadHeartbeatInfo> threadHeartbeats;
    private final Duration defaultHeartbeatTimeout;
    
    public ThreadHeartbeatRegistry(Duration defaultHeartbeatTimeout) {
        this.threadHeartbeats = new ConcurrentHashMap<>();
        this.defaultHeartbeatTimeout = defaultHeartbeatTimeout;
        logger.info("ThreadHeartbeatRegistry initialized with default timeout: {}", defaultHeartbeatTimeout);
    }
    
    /**
     * Registers a new thread for monitoring with default timeout.
     * 
     * @param threadId unique identifier for the thread
     * @param threadName human-readable name for the thread
     */
    public void registerThread(String threadId, String threadName) {
        registerThread(threadId, threadName, defaultHeartbeatTimeout);
    }
    
    /**
     * Registers a new thread for monitoring with custom timeout.
     * 
     * @param threadId unique identifier for the thread
     * @param threadName human-readable name for the thread
     * @param heartbeatTimeout maximum time allowed between heartbeats
     */
    public void registerThread(String threadId, String threadName, Duration heartbeatTimeout) {
        ThreadHeartbeatInfo info = new ThreadHeartbeatInfo(threadName, heartbeatTimeout, Instant.now());
        threadHeartbeats.put(threadId, info);
        logger.info("Registered thread for monitoring: {} ({}), timeout: {}", threadName, threadId, heartbeatTimeout);
    }
    
    /**
     * Records a heartbeat for the specified thread.
     * 
     * @param threadId unique identifier for the thread
     */
    public void recordHeartbeat(String threadId) {
        recordHeartbeat(threadId, null);
    }
    
    /**
     * Records a heartbeat for the specified thread with additional status information.
     * 
     * @param threadId unique identifier for the thread
     * @param status optional status message
     */
    public void recordHeartbeat(String threadId, String status) {
        ThreadHeartbeatInfo info = threadHeartbeats.get(threadId);
        if (info != null) {
            info.updateHeartbeat(status);
            logger.debug("Heartbeat recorded for thread: {} at {}", threadId, info.getLastHeartbeat());
        } else {
            logger.warn("Attempted to record heartbeat for unregistered thread: {}", threadId);
        }
    }
    
    /**
     * Unregisters a thread from monitoring.
     * 
     * @param threadId unique identifier for the thread
     */
    public void unregisterThread(String threadId) {
        ThreadHeartbeatInfo removed = threadHeartbeats.remove(threadId);
        if (removed != null) {
            logger.info("Unregistered thread from monitoring: {} ({})", removed.getThreadName(), threadId);
        }
    }
    
    /**
     * Checks the health of all registered threads.
     * 
     * @return set of thread IDs that have missed their heartbeat deadline
     */
    public Set<String> getUnhealthyThreads() {
        Instant now = Instant.now();
        return threadHeartbeats.entrySet().stream()
            .filter(entry -> {
                ThreadHeartbeatInfo info = entry.getValue();
                Duration timeSinceLastHeartbeat = Duration.between(info.getLastHeartbeat(), now);
                return timeSinceLastHeartbeat.compareTo(info.getHeartbeatTimeout()) > 0;
            })
            .map(Map.Entry::getKey)
            .collect(Collectors.toSet());
    }
    
    /**
     * Gets detailed health information for all registered threads.
     * 
     * @return map of thread IDs to their health status
     */
    public Map<String, ThreadHealthStatus> getThreadHealthStatus() {
        Instant now = Instant.now();
        return threadHeartbeats.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> {
                    ThreadHeartbeatInfo info = entry.getValue();
                    Duration timeSinceLastHeartbeat = Duration.between(info.getLastHeartbeat(), now);
                    boolean isHealthy = timeSinceLastHeartbeat.compareTo(info.getHeartbeatTimeout()) <= 0;
                    return new ThreadHealthStatus(
                        info.getThreadName(),
                        info.getLastHeartbeat(),
                        timeSinceLastHeartbeat,
                        info.getHeartbeatTimeout(),
                        isHealthy,
                        info.getLastStatus()
                    );
                }
            ));
    }
    
    /**
     * Gets the number of registered threads.
     * 
     * @return number of threads currently being monitored
     */
    public int getRegisteredThreadCount() {
        return threadHeartbeats.size();
    }
    
    /**
     * Checks if a specific thread is registered.
     * 
     * @param threadId unique identifier for the thread
     * @return true if the thread is registered for monitoring
     */
    public boolean isThreadRegistered(String threadId) {
        return threadHeartbeats.containsKey(threadId);
    }
    
    /**
     * Gets the thread name for a given thread ID.
     * 
     * @param threadId unique identifier for the thread
     * @return thread name or null if not found
     */
    public String getThreadName(String threadId) {
        ThreadHeartbeatInfo info = threadHeartbeats.get(threadId);
        return info != null ? info.getThreadName() : null;
    }
}
