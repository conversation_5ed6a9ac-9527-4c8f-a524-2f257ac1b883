package fr.enedis.i2r.system.watchdog;

import java.time.Duration;
import java.time.Instant;

/**
 * Immutable record representing the health status of a monitored thread.
 * Contains all relevant information about a thread's current state.
 */
public record ThreadHealthStatus(
    String threadName,
    Instant lastHeartbeat,
    Duration timeSinceLastHeartbeat,
    Duration heartbeatTimeout,
    boolean isHealthy,
    String lastStatus
) {
    
    /**
     * Gets a human-readable description of the thread's health status.
     * 
     * @return status description
     */
    public String getStatusDescription() {
        if (isHealthy) {
            return String.format("Healthy - Last heartbeat %d seconds ago", 
                timeSinceLastHeartbeat.toSeconds());
        } else {
            return String.format("Unhealthy - Last heartbeat %d seconds ago (timeout: %d seconds)", 
                timeSinceLastHeartbeat.toSeconds(), 
                heartbeatTimeout.toSeconds());
        }
    }
    
    /**
     * Gets the percentage of timeout elapsed since last heartbeat.
     * 
     * @return percentage (0.0 to 1.0+) where 1.0+ indicates timeout exceeded
     */
    public double getTimeoutPercentage() {
        return (double) timeSinceLastHeartbeat.toMillis() / heartbeatTimeout.toMillis();
    }
}
