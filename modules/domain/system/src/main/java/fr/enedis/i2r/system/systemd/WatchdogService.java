package fr.enedis.i2r.system.systemd;

import fr.enedis.i2r.system.SystemConfiguration;
import fr.enedis.i2r.system.ports.WatchdogSocketPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WatchdogService implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(WatchdogService.class);

    private final WatchdogSocketPort notifySocket;
    private final SystemConfiguration configuration;

    public WatchdogService(WatchdogSocketPort notifySocket, SystemConfiguration configuration) {
        this.notifySocket = notifySocket;
        this.configuration = configuration;
    }

    @Override
    public void run() {
        try {
            logger.info("Service Watchdog démarré");
            notifySocket.init();
            while (true) {
                notifySocket.heartbeat();
                Thread.sleep(configuration.watchdogPeriod());
            }
        } catch (Exception e) {
            logger.error("Erreur pendant le démarrage du service Watchdog", e);
        }
    }
}
