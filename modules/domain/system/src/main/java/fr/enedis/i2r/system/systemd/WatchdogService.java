package fr.enedis.i2r.system.systemd;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.system.SystemConfiguration;
import fr.enedis.i2r.system.ports.WatchdogSocketPort;
import fr.enedis.i2r.system.watchdog.ThreadHealthStatus;
import fr.enedis.i2r.system.watchdog.ThreadHeartbeatRegistry;

public class WatchdogService implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(WatchdogService.class);

    private final WatchdogSocketPort notifySocket;
    private final SystemConfiguration configuration;
    private final ThreadHeartbeatRegistry heartbeatRegistry;
    private volatile boolean running = true;

    public WatchdogService(WatchdogSocketPort notifySocket, SystemConfiguration configuration) {
        this(notifySocket, configuration, null);
    }

    public WatchdogService(WatchdogSocketPort notifySocket, SystemConfiguration configuration,
                          ThreadHeartbeatRegistry heartbeatRegistry) {
        this.notifySocket = notifySocket;
        this.configuration = configuration;
        this.heartbeatRegistry = heartbeatRegistry;
    }

    @Override
    public void run() {
        try {
            logger.info("Service Watchdog démarré");
            notifySocket.init();

            while (running && !Thread.currentThread().isInterrupted()) {
                if (heartbeatRegistry != null) {
                    performMultiThreadHealthCheck();
                } else {
                    // Legacy single-thread mode
                    notifySocket.heartbeat();
                }
                Thread.sleep(configuration.watchdogPeriod());
            }
        } catch (InterruptedException e) {
            logger.info("Watchdog service interrupted");
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            logger.error("Erreur pendant le démarrage du service Watchdog", e);
        } finally {
            logger.info("Watchdog service stopped");
        }
    }

    private void performMultiThreadHealthCheck() {
        Set<String> unhealthyThreads = heartbeatRegistry.getUnhealthyThreads();
        Map<String, ThreadHealthStatus> allThreadStatus = heartbeatRegistry.getThreadHealthStatus();

        if (unhealthyThreads.isEmpty()) {
            // All threads are healthy
            String statusMessage = buildHealthyStatusMessage(allThreadStatus);
            notifySocket.heartbeatWithMessage(statusMessage);
            logger.debug("All {} monitored threads are healthy", allThreadStatus.size());
        } else {
            // Some threads are unhealthy
            String statusMessage = buildUnhealthyStatusMessage(unhealthyThreads, allThreadStatus);
            notifySocket.heartbeatWithMessage(statusMessage);
            logger.warn("Unhealthy threads detected: {}", unhealthyThreads);

            // Log detailed information about unhealthy threads
            for (String threadId : unhealthyThreads) {
                ThreadHealthStatus status = allThreadStatus.get(threadId);
                if (status != null) {
                    logger.warn("Thread {} ({}): {}",
                        status.threadName(), threadId, status.getStatusDescription());
                }
            }
        }
    }

    private String buildHealthyStatusMessage(Map<String, ThreadHealthStatus> allThreadStatus) {
        return String.format("All %d threads healthy", allThreadStatus.size());
    }

    private String buildUnhealthyStatusMessage(Set<String> unhealthyThreads,
                                             Map<String, ThreadHealthStatus> allThreadStatus) {
        String unhealthyNames = unhealthyThreads.stream()
            .map(threadId -> {
                ThreadHealthStatus status = allThreadStatus.get(threadId);
                return status != null ? status.threadName() : threadId;
            })
            .collect(Collectors.joining(", "));

        return String.format("%d/%d threads unhealthy: %s",
            unhealthyThreads.size(), allThreadStatus.size(), unhealthyNames);
    }

    public void stop() {
        logger.info("Stopping watchdog service...");
        running = false;
    }

    public ThreadHeartbeatRegistry getHeartbeatRegistry() {
        return heartbeatRegistry;
    }
}
