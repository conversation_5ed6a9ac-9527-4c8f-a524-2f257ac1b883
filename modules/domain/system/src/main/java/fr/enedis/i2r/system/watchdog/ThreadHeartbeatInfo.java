package fr.enedis.i2r.system.watchdog;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Thread-safe container for heartbeat information of a monitored thread.
 * This class stores the thread's metadata and tracks its last heartbeat timestamp.
 */
public class ThreadHeartbeatInfo {
    
    private final String threadName;
    private final Duration heartbeatTimeout;
    private final AtomicReference<Instant> lastHeartbeat;
    private final AtomicReference<String> lastStatus;
    
    public ThreadHeartbeatInfo(String threadName, Duration heartbeatTimeout, Instant initialHeartbeat) {
        this.threadName = threadName;
        this.heartbeatTimeout = heartbeatTimeout;
        this.lastHeartbeat = new AtomicReference<>(initialHeartbeat);
        this.lastStatus = new AtomicReference<>();
    }
    
    /**
     * Updates the heartbeat timestamp to the current time.
     */
    public void updateHeartbeat() {
        updateHeartbeat(null);
    }
    
    /**
     * Updates the heartbeat timestamp and status.
     * 
     * @param status optional status message
     */
    public void updateHeartbeat(String status) {
        lastHeartbeat.set(Instant.now());
        lastStatus.set(status);
    }
    
    /**
     * Gets the thread name.
     * 
     * @return thread name
     */
    public String getThreadName() {
        return threadName;
    }
    
    /**
     * Gets the heartbeat timeout duration.
     * 
     * @return timeout duration
     */
    public Duration getHeartbeatTimeout() {
        return heartbeatTimeout;
    }
    
    /**
     * Gets the timestamp of the last heartbeat.
     * 
     * @return last heartbeat timestamp
     */
    public Instant getLastHeartbeat() {
        return lastHeartbeat.get();
    }
    
    /**
     * Gets the last status message.
     * 
     * @return last status message or null if none
     */
    public String getLastStatus() {
        return lastStatus.get();
    }
}
