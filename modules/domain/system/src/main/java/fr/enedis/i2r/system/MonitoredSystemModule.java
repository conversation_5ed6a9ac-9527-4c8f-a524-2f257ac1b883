package fr.enedis.i2r.system;

import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.system.leds.LedName;
import fr.enedis.i2r.system.leds.LedStatus;
import fr.enedis.i2r.system.ports.LedPort;
import fr.enedis.i2r.system.ports.SystemModulePort;
import fr.enedis.i2r.system.ports.SystemdPort;
import fr.enedis.i2r.system.watchdog.AbstractMonitoredThread;

/**
 * Monitored wrapper for the SystemModule that integrates with the watchdog system.
 * This class extends AbstractMonitoredThread to provide heartbeat monitoring and
 * performs periodic system health checks.
 */
public class MonitoredSystemModule extends AbstractMonitoredThread implements SystemModulePort {
    
    private static final Logger logger = LoggerFactory.getLogger(MonitoredSystemModule.class);
    private static final Duration HEARTBEAT_TIMEOUT = Duration.ofMinutes(3);
    private static final Duration HEALTH_CHECK_INTERVAL = Duration.ofMinutes(1);
    
    private final SystemModule systemModule;
    private final SystemConfiguration systemConfiguration;
    private final LedPort ledPort;
    private final SystemdPort systemdPort;
    
    public MonitoredSystemModule(SystemConfiguration systemConfiguration, LedPort ledPort, SystemdPort systemdPort) {
        super("SystemModule", HEARTBEAT_TIMEOUT);
        this.systemConfiguration = systemConfiguration;
        this.ledPort = ledPort;
        this.systemdPort = systemdPort;
        this.systemModule = new SystemModule(systemConfiguration, ledPort, systemdPort);
    }
    
    @Override
    protected void doRun() throws InterruptedException {
        try {
            sendHeartbeat("Starting system module");
            logger.info("Starting monitored system module");
            
            // Perform initial system setup
            systemModule.run();
            sendHeartbeat("System module initialized");
            
            // Perform periodic health checks
            while (isRunning()) {
                performHealthCheck();
                Thread.sleep(HEALTH_CHECK_INTERVAL.toMillis());
            }
            
        } catch (InterruptedException e) {
            logger.info("Monitored system module interrupted");
            throw e;
        } catch (Exception e) {
            logger.error("Error in monitored system module", e);
            sendHeartbeat("Error: " + e.getMessage());
        } finally {
            logger.info("Monitored system module stopped");
        }
    }
    
    private void performHealthCheck() {
        try {
            // Check LED status
            LedStatus powerLedStatus = ledPort.getLedStatus(LedName.POWER);
            LedStatus rslLedStatus = ledPort.getLedStatus(LedName.RSL);
            
            String status = String.format("Power LED: %s, RSL LED: %s", powerLedStatus, rslLedStatus);
            sendHeartbeat(status);
            
            logger.debug("System health check completed: {}", status);
            
        } catch (Exception e) {
            logger.warn("Error during system health check", e);
            sendHeartbeat("Health check error: " + e.getMessage());
        }
    }
    
    // Delegate all SystemModulePort methods to the wrapped SystemModule
    
    @Override
    public void rebootBip() {
        systemModule.rebootBip();
        sendHeartbeat("Bip reboot requested");
    }
    
    @Override
    public void stopConnection() {
        systemModule.stopConnection();
        sendHeartbeat("Connection stopped");
    }
    
    @Override
    public void startConnection() {
        systemModule.startConnection();
        sendHeartbeat("Connection started");
    }
    
    @Override
    public void setRslLed(LedStatus status) {
        systemModule.setRslLed(status);
        sendHeartbeat("RSL LED set to: " + status);
    }
    
    @Override
    public void activateSecondaryServices() {
        systemModule.activateSecondaryServices();
        sendHeartbeat("Secondary services activated");
    }
    
    @Override
    public void startSecondaryServices() {
        systemModule.startSecondaryServices();
        sendHeartbeat("Secondary services started");
    }
}
