package fr.enedis.i2r.comsi;

import java.time.Clock;
import java.time.Duration;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;
import fr.enedis.i2r.comsi.ports.ModuleSecuritePort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.system.watchdog.AbstractMonitoredThread;
import fr.enedis.i2r.system.watchdog.ThreadHeartbeatRegistry;

/**
 * Monitored wrapper for the ComSI module that integrates with the watchdog system.
 * This class extends AbstractMonitoredThread to provide heartbeat monitoring and
 * manages the ConfigurationChangeWatcher as a monitored sub-thread.
 */
public class MonitoredComSI extends AbstractMonitoredThread {

    private static final Logger logger = LoggerFactory.getLogger(MonitoredComSI.class);
    private static final Duration HEARTBEAT_TIMEOUT = Duration.ofMinutes(3);
    private static final Duration HEARTBEAT_INTERVAL = Duration.ofSeconds(30);

    private final ComSiConfiguration comSiConfiguration;
    private final ComsiParametersPort parametersPort;
    private final SiClientPort siClientPort;
    private final BoardManagerPort boardManagerPort;
    private final ModuleSecuritePort moduleSecuritePort;
    private final ModemManagerPort modemManagerPort;
    private final DatabaseUpdateWatcherPort dbUpdateWatcherPort;
    private final Clock clock;

    private ConfigurationChangeWatcher configurationChangeWatcher;
    private ExecutorService executorService;

    public MonitoredComSI(ComSiConfiguration comSiConfiguration,
                         ComsiParametersPort parametersPort,
                         SiClientPort siClientPort,
                         BoardManagerPort boardManagerPort,
                         ModuleSecuritePort moduleSecuritePort,
                         ModemManagerPort modemManager,
                         DatabaseUpdateWatcherPort dbUpdateWatcherPort,
                         Clock clock) {
        super("ComSI", HEARTBEAT_TIMEOUT);

        this.comSiConfiguration = comSiConfiguration;
        this.parametersPort = parametersPort;
        this.siClientPort = siClientPort;
        this.boardManagerPort = boardManagerPort;
        this.moduleSecuritePort = moduleSecuritePort;
        this.modemManagerPort = modemManager;
        this.dbUpdateWatcherPort = dbUpdateWatcherPort;
        this.clock = clock;
    }

    @Override
    protected void doRun() throws InterruptedException {
        try {
            sendHeartbeat("Starting ComSI");
            logger.info("Starting monitored ComSI");

            // Perform initial configuration if needed
            if (comSiConfiguration.bipStatus() == BipStatus.INIT) {
                sendHeartbeat("Sending initial configuration to iCOM");
                logger.debug("Sending configuration to iCOM");

                ConfigurationBoitier config = ConfigurationBoitier.from(
                    comSiConfiguration,
                    clock.instant(),
                    parametersPort.getConfigurationHash(),
                    boardManagerPort.getAds(),
                    moduleSecuritePort.getIdms(),
                    modemManagerPort.getIccid()
                );
                siClientPort.sendConfigurationBoitier(config);
                sendHeartbeat("Initial configuration sent");
            }

            // Start the configuration change watcher as a monitored thread
            configurationChangeWatcher = new ConfigurationChangeWatcher(
                dbUpdateWatcherPort,
                siClientPort.getSiConfigurationNotifier()
            );

            // Set the heartbeat registry for the configuration change watcher
            ThreadHeartbeatRegistry registry = getHeartbeatRegistry();
            if (registry != null) {
                configurationChangeWatcher.setHeartbeatRegistry(registry);
            }

            executorService = Executors.newFixedThreadPool(1);
            executorService.submit(configurationChangeWatcher);
            sendHeartbeat("Configuration change watcher started");

            logger.info("ComSI en cours de fonctionnement");

            // Monitor the ComSI with periodic heartbeats
            while (isRunning()) {
                sendHeartbeat("ComSI running normally");

                // Check if the configuration change watcher is still running
                if (executorService.isShutdown() || executorService.isTerminated()) {
                    logger.error("Configuration change watcher executor has stopped");
                    sendHeartbeat("Configuration watcher stopped unexpectedly");
                    break;
                }

                Thread.sleep(HEARTBEAT_INTERVAL.toMillis());
            }

        } catch (InterruptedException e) {
            logger.info("Monitored ComSI interrupted");
            throw e;
        } catch (Exception e) {
            logger.error("Error in monitored ComSI", e);
            sendHeartbeat("Error: " + e.getMessage());
        } finally {
            // Clean up resources
            if (configurationChangeWatcher != null) {
                configurationChangeWatcher.stop();
            }
            if (executorService != null && !executorService.isShutdown()) {
                executorService.shutdown();
                try {
                    if (!executorService.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                        executorService.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    executorService.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
            logger.info("Monitored ComSI stopped");
        }
    }
}
