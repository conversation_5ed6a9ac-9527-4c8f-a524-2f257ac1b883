package fr.enedis.i2r.comsi.rest;

import java.time.Duration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;
import fr.enedis.i2r.comsi.ports.ModuleSecuritePort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.comsi.status.BipStatusManager;
import fr.enedis.i2r.system.LoggingLoader;
import fr.enedis.i2r.system.ports.ShellExecutorPort;
import fr.enedis.i2r.system.watchdog.AbstractMonitoredThread;
import io.javalin.Javalin;
import io.javalin.community.ssl.SslPlugin;

/**
 * Monitored wrapper for the HTTPS server that integrates with the watchdog system.
 * This class extends AbstractMonitoredThread to provide heartbeat monitoring.
 */
public class MonitoredHttpsServer extends AbstractMonitoredThread {
    
    private static final Logger logger = LoggerFactory.getLogger(MonitoredHttpsServer.class);
    private static final Duration HEARTBEAT_TIMEOUT = Duration.ofMinutes(5);
    private static final Duration HEARTBEAT_INTERVAL = Duration.ofMinutes(1);
    
    private final HttpsServer httpsServer;
    private Javalin app;
    
    public MonitoredHttpsServer(BipStatusManager bipStatusManager, 
                               ComSiConfiguration comSiConfiguration,
                               ShellExecutorPort shellExecutorPort, 
                               LoggingLoader loggingLoader,
                               SslPlugin sslPlugin, 
                               ComsiParametersPort parametersPort, 
                               BoardManagerPort boardManagerPort,
                               ModuleSecuritePort moduleSecuritePort, 
                               ModemManagerPort modemManagerPort, 
                               SiClientPort siClientPort) {
        super("HttpsServer", HEARTBEAT_TIMEOUT);
        
        this.httpsServer = new HttpsServer(bipStatusManager, comSiConfiguration, shellExecutorPort, 
                                         loggingLoader, sslPlugin, parametersPort, boardManagerPort, 
                                         moduleSecuritePort, modemManagerPort, siClientPort);
    }
    
    @Override
    protected void doRun() throws InterruptedException {
        try {
            sendHeartbeat("Starting HTTPS server");
            logger.info("Starting monitored HTTPS server");
            
            // Start the HTTPS server in a separate thread
            Thread serverThread = new Thread(() -> {
                try {
                    httpsServer.run();
                } catch (Exception e) {
                    logger.error("Error in HTTPS server", e);
                }
            });
            serverThread.setDaemon(true);
            serverThread.start();
            
            // Wait a bit for the server to start
            Thread.sleep(2000);
            sendHeartbeat("HTTPS server started");
            
            // Monitor the server with periodic heartbeats
            while (isRunning()) {
                sendHeartbeat("HTTPS server running");
                
                // Check if the server thread is still alive
                if (!serverThread.isAlive()) {
                    logger.error("HTTPS server thread has died");
                    sendHeartbeat("HTTPS server thread died");
                    break;
                }
                
                Thread.sleep(HEARTBEAT_INTERVAL.toMillis());
            }
            
        } catch (InterruptedException e) {
            logger.info("Monitored HTTPS server interrupted");
            throw e;
        } catch (Exception e) {
            logger.error("Error in monitored HTTPS server", e);
            sendHeartbeat("Error: " + e.getMessage());
        } finally {
            if (app != null) {
                try {
                    app.stop();
                    sendHeartbeat("HTTPS server stopped");
                } catch (Exception e) {
                    logger.error("Error stopping HTTPS server", e);
                }
            }
            logger.info("Monitored HTTPS server stopped");
        }
    }
}
