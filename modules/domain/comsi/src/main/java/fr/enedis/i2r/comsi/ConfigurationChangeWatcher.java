package fr.enedis.i2r.comsi;

import java.time.Duration;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.params.ConfigurationValue;
import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.si.SiConfigurationNotifierPort;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.system.watchdog.AbstractMonitoredThread;

/**
 * Le but de cette classe est d'observer tous les changements de configuration du boitier.
 * Cette classe hérite maintenant d'AbstractMonitoredThread pour être surveillée par le watchdog.
 */
public class ConfigurationChangeWatcher extends AbstractMonitoredThread {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationChangeWatcher.class);
    private static final Duration HEARTBEAT_TIMEOUT = Duration.ofMinutes(2);

    private DatabaseUpdateWatcherPort databaseUpdateWatcherPort;
    private SiConfigurationNotifierPort siNotifierPort;

    public ConfigurationChangeWatcher(DatabaseUpdateWatcherPort databaseUpdatePort, SiConfigurationNotifierPort siNotifierPort) {
        super("ConfigurationChangeWatcher", HEARTBEAT_TIMEOUT);
        this.databaseUpdateWatcherPort = databaseUpdatePort;
        this.siNotifierPort = siNotifierPort;
    }

    @Override
    protected void doRun() throws InterruptedException {
        logger.info("Configuration change watcher started");
        sendHeartbeat("Started");

        while (isRunning()) {
            try {
                sendHeartbeat("Waiting for database updates");
                List<ConfigurationValue> updatedValues = this.databaseUpdateWatcherPort
                    .waitForUpdates()
                    .stream().filter(confValue -> confValue.parameter().watched)
                    .toList();

                sendHeartbeat("Processing " + updatedValues.size() + " configuration changes");
                for (ConfigurationValue updatedValue: updatedValues) {
                    this.notifySi(updatedValue);
                }
                sendHeartbeat("Configuration changes processed");
            } catch (InterruptedException e) {
                logger.info("Configuration change watcher interrupted, stopping...");
                throw e; // Re-throw to let AbstractMonitoredThread handle it
            } catch (Exception e) {
                logger.error("erreur lors de l'écoute d'un changement de configuration", e);
                sendHeartbeat("Error: " + e.getMessage());
                // Continue running after error
            }
        }
        logger.info("Configuration change watcher stopped");
    }

    private void notifySi(ConfigurationValue updatedConfigurationValue) throws Exception {
        switch (updatedConfigurationValue.parameter()) {
            case BipState -> this.handleStateChange(updatedConfigurationValue.value());
        }
    }

    private void handleStateChange(String newState) throws NumberFormatException, Exception {
        Integer stateValue = Integer.parseInt(newState);

        BipStatus bipStatus = BipStatus.fromStatusCode(stateValue)
            .orElseThrow(() -> new Exception(String.format("nouvel état du boitier invalide: %d", stateValue)));

        this.siNotifierPort.notifyStateChange(bipStatus);
    }
}
