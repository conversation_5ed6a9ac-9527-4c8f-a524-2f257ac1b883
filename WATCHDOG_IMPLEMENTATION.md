# Enhanced Watchdog/Heartbeat System Implementation

## Overview

This implementation provides a comprehensive, thread-safe watchdog system that monitors both the main thread and all spawned sub-threads (including ComSI and ConfigurationChangeWatcher). The system is based on timestamp-based heartbeats and integrates seamlessly with the existing systemd watchdog mechanism.

## Key Features

- **Thread-Safe**: All components use concurrent data structures and atomic operations
- **Timestamp-Based**: Uses `Instant` timestamps for precise heartbeat tracking
- **Configurable**: Heartbeat timeouts and intervals are configurable via system parameters
- **Comprehensive Monitoring**: Monitors all critical threads including ComSI, ConfigurationChangeWatcher, HttpsServer, and SystemModule
- **Unix Socket Integration**: Enhanced to send detailed status messages to systemd
- **Graceful Degradation**: Falls back to legacy mode if no threads are registered

## Architecture

### Core Components

1. **ThreadHeartbeatRegistry**: Central registry for managing thread heartbeats
2. **AbstractMonitoredThread**: Base class for threads that need monitoring
3. **MonitoredThread Interface**: Contract for monitored threads
4. **Enhanced WatchdogService**: Extended to monitor multiple threads
5. **Monitored Service Wrappers**: Wrapper classes for existing services

### Class Hierarchy

```
MonitoredThread (interface)
    ↑
AbstractMonitoredThread (abstract class)
    ↑
├── ConfigurationChangeWatcher
├── MonitoredComSI
├── MonitoredHttpsServer
└── MonitoredSystemModule
```

## Implementation Details

### ThreadHeartbeatRegistry

- **Thread-Safe**: Uses `ConcurrentHashMap` for thread storage
- **Atomic Operations**: Uses `AtomicReference` for timestamp updates
- **Health Monitoring**: Provides real-time health status for all threads
- **Configurable Timeouts**: Supports per-thread timeout configuration

### AbstractMonitoredThread

- **Lifecycle Management**: Handles thread registration/unregistration automatically
- **Exception Handling**: Gracefully handles exceptions and ensures cleanup
- **Heartbeat API**: Simple `sendHeartbeat()` methods for status reporting
- **Interruption Support**: Properly handles thread interruption

### Enhanced WatchdogService

- **Multi-Thread Monitoring**: Checks health of all registered threads
- **Detailed Status Messages**: Sends comprehensive status to systemd
- **Backward Compatibility**: Maintains compatibility with existing single-thread mode
- **Configurable Intervals**: Uses system configuration for timing

## Configuration

### New System Configuration Parameters

```java
// Default values
Duration DEFAULT_THREAD_HEARTBEAT_TIMEOUT = Duration.ofMinutes(2);
Duration DEFAULT_THREAD_HEARTBEAT_INTERVAL = Duration.ofSeconds(30);

// Configuration parameters
i2r.watchdog.thread-heartbeat-timeout-secs=120
i2r.watchdog.thread-heartbeat-interval-secs=30
```

### Thread-Specific Timeouts

Each monitored thread can specify its own heartbeat timeout:

- **ConfigurationChangeWatcher**: 2 minutes (may wait for database changes)
- **HttpsServer**: 5 minutes (HTTP server should be stable)
- **SystemModule**: 3 minutes (system operations)
- **ComSI**: 3 minutes (communication operations)

## Usage Example

### Creating a Monitored Thread

```java
public class MyMonitoredService extends AbstractMonitoredThread {
    public MyMonitoredService() {
        super("MyService", Duration.ofMinutes(2));
    }
    
    @Override
    protected void doRun() throws InterruptedException {
        while (isRunning()) {
            // Do work
            sendHeartbeat("Processing data");
            Thread.sleep(1000);
        }
    }
}
```

### Integration in Main

```java
// Initialize registry
ThreadHeartbeatRegistry registry = new ThreadHeartbeatRegistry(
    systemConfiguration.threadHeartbeatTimeout()
);

// Create monitored services
MyMonitoredService service = new MyMonitoredService();
service.setHeartbeatRegistry(registry);

// Start enhanced watchdog
WatchdogService watchdog = new WatchdogService(
    watchdogSocket, systemConfiguration, registry
);
```

## Status Messages

The enhanced watchdog sends detailed status messages to systemd:

### Healthy Status
```
WATCHDOG=1
STATUS=All 4 threads healthy
```

### Unhealthy Status
```
WATCHDOG=1
STATUS=1/4 threads unhealthy: ConfigurationChangeWatcher
```

## Testing

Comprehensive test suite includes:

1. **Unit Tests**: `ThreadHeartbeatRegistryTest`, `AbstractMonitoredThreadTest`
2. **Integration Tests**: `WatchdogIntegrationTest`
3. **Thread Safety Tests**: Concurrent access scenarios
4. **Timeout Tests**: Heartbeat timeout detection
5. **Exception Handling Tests**: Error scenarios

## Migration Guide

### Existing Services

The implementation provides monitored wrappers for existing services:

- `ComSI` → `MonitoredComSI`
- `HttpsServer` → `MonitoredHttpsServer`
- `SystemModule` → `MonitoredSystemModule`
- `ConfigurationChangeWatcher` → Enhanced with monitoring

### Backward Compatibility

- Existing `WatchdogService` constructor still works (legacy mode)
- No changes required to existing `WatchdogSocketPort` implementations
- Configuration parameters have sensible defaults

## Benefits

1. **Improved Reliability**: Early detection of thread failures
2. **Better Observability**: Detailed status information in systemd logs
3. **Proactive Monitoring**: Timestamp-based detection prevents false positives
4. **Scalable Design**: Easy to add new monitored threads
5. **Production Ready**: Thread-safe and thoroughly tested

## Future Enhancements

1. **Metrics Integration**: Export heartbeat metrics to monitoring systems
2. **Custom Actions**: Configurable actions when threads become unhealthy
3. **Health Checks**: Integration with application health check endpoints
4. **Dynamic Configuration**: Runtime configuration updates
5. **Thread Dependencies**: Model dependencies between threads
